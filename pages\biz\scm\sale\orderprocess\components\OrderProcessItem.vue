<template>
	<view class="order-card" @click="handleOrderDetail">
		<view class="order-header">
			<view class="order-info">
				<view class="order-no">{{ orderData.orderNo || orderData.salesOrderInfo?.orderNo }}</view>
				<view class="order-type-badge fulfillment">履约</view>
				<view class="order-priority" v-if="orderData.priority" :class="'priority-' + orderData.priority">
					{{
						orderData.priority === 'high' ? '高' :
						orderData.priority === 'low' ? '低' : '中'
					}}
				</view>
			</view>
			<view class="order-quantity">
				<text class="quantity-main">{{ getOrderQuantity() }}</text>
				<text class="quantity-completed" v-if="getCompletedQuantity()">
					已完成: {{ getCompletedQuantity() }}
				</text>
			</view>
		</view>

		<view class="order-progress">
			<view class="progress-info">
				<text class="progress-label">整体进度</text>
				<text class="progress-value">{{ orderData.progress || 0 }}%</text>
			</view>
			<uv-line-progress
				:percentage="orderData.progress || 0"
				:show-text="false"
				height="6"
				active-color="#409eff"
				inactive-color="#e4e7ed"
			></uv-line-progress>
		</view>

		<!-- 轮播容器 -->
		<view class="carousel-container">
			<swiper
				class="carousel-swiper"
				:indicator-dots="true"
				:autoplay="false"
				:interval="3000"
				:duration="500"
				indicator-color="rgba(0, 0, 0, .3)"
				indicator-active-color="#409eff"
				@change="onSwiperChange"
			>
				<!-- 第一页：订单信息、产品信息、数量、需求状态 -->
				<swiper-item class="swiper-item">
					<view class="carousel-page">
						<view class="page-title">
							<uv-icon name="file-text" size="16" color="#409eff"></uv-icon>
							<text class="title-text">订单详情</text>
						</view>

						<view class="info-section">
							<view class="info-row">
								<view class="info-label">客户信息</view>
								<view class="info-value">{{ getCustomerName() }}</view>
							</view>
							<view class="info-row">
								<view class="info-label">下单时间</view>
								<view class="info-value">{{ formatDate(getOrderDate()) }}</view>
							</view>
							<view class="info-row">
								<view class="info-label">发货时间</view>
								<view class="info-value">{{ formatDate(getDeliveryTime()) }}</view>
							</view>
							<view class="info-row">
								<view class="info-label">客户要求</view>
								<view class="info-value">{{ getRequirement() }}</view>
							</view>
						</view>

						<view class="info-section">
							<view class="section-title">产品信息</view>
							<view class="product-detail">
								<view class="product-name">{{ getProductName() }}</view>
								<view class="product-code">{{ getProductCode() }}</view>
								<view class="product-spec">{{ getProductSpec() }}</view>
							</view>
						</view>

						<view class="info-section">
							<view class="section-title">数量信息</view>
							<view class="quantity-detail">
								<view class="quantity-row">
									<text class="quantity-label">订单数量:</text>
									<text class="quantity-value">{{ getOrderQuantity() }}</text>
								</view>
								<view class="quantity-row">
									<text class="quantity-label">已完成:</text>
									<text class="quantity-value completed">{{ getCompletedQuantity() }}</text>
								</view>
								<view class="quantity-row" v-if="getSpecQuantity()">
									<text class="quantity-label">规格数量:</text>
									<text class="quantity-value">{{ getSpecQuantity() }}</text>
								</view>
							</view>
						</view>

						<view class="info-section" v-if="getRequestStatus()">
							<view class="section-title">需求状态</view>
							<view class="status-detail">
								<view class="status-item">
									<text class="status-label">状态:</text>
									<view class="status-badge" :class="getRequestStatusClass()">
										{{ getRequestStatusText() }}
									</view>
								</view>
								<view class="status-item" v-if="getRequestCreateTime()">
									<text class="status-label">创建时间:</text>
									<text class="status-value">{{ formatDate(getRequestCreateTime()) }}</text>
								</view>
								<view class="status-item" v-if="getRequestConfirmTime()">
									<text class="status-label">BOM确认:</text>
									<text class="status-value">{{ formatDate(getRequestConfirmTime()) }}</text>
								</view>
							</view>
						</view>
					</view>
				</swiper-item>

				<!-- 第二页：原料库存 -->
				<swiper-item class="swiper-item">
					<view class="carousel-page">
						<view class="page-title">
							<uv-icon name="package" size="16" color="#67c23a"></uv-icon>
							<text class="title-text">原料库存</text>
						</view>

						<view class="process-section">
							<view class="process-header">
								<view class="process-status" :class="getMaterialStatusClass()">
									{{ getMaterialStatusText() }}
								</view>
								<view class="process-progress">
									<text class="progress-text">{{ getMaterialProgress() }}%</text>
									<view class="progress-bar">
										<view class="progress-fill"
											:class="getMaterialProgressClass()"
											:style="{ width: getMaterialProgress() + '%' }">
										</view>
									</view>
								</view>
							</view>

							<view class="material-list" v-if="getMaterialList().length > 0">
								<view class="material-item" v-for="(item, index) in getMaterialList()" :key="index">
									<view class="material-info">
										<text class="material-name">{{ item.name || item.fullCode }}</text>
										<text class="material-code">{{ item.fullCode }}</text>
									</view>
									<view class="material-quantity">
										<text class="quantity-text">{{ item.pendingQty || 0 }} {{ item.unitName || '' }}</text>
										<view class="material-status" :class="item.shortage ? 'shortage' : 'sufficient'">
											{{ item.shortage ? `缺料${item.shortageQty || 0}` : '已备齐' }}
										</view>
									</view>
								</view>
							</view>
							<view v-else class="empty-state">
								<text class="empty-text">暂无原料信息</text>
							</view>
						</view>
					</view>
				</swiper-item>

				<!-- 第三页：采购 -->
				<swiper-item class="swiper-item">
					<view class="carousel-page">
						<view class="page-title">
							<uv-icon name="shopping-cart" size="16" color="#e6a23c"></uv-icon>
							<text class="title-text">采购进度</text>
						</view>

						<view class="process-section">
							<view class="process-header">
								<view class="process-status" :class="getProcurementStatusClass()">
									{{ getProcurementStatusText() }}
								</view>
								<view class="process-progress">
									<text class="progress-text">{{ getProcurementProgress() }}%</text>
									<view class="progress-bar">
										<view class="progress-fill"
											:class="getProcurementProgressClass()"
											:style="{ width: getProcurementProgress() + '%' }">
										</view>
									</view>
								</view>
							</view>

							<view class="purchase-list" v-if="getPurchaseList().length > 0">
								<view class="purchase-item" v-for="(item, index) in getPurchaseList()" :key="index">
									<view class="purchase-info">
										<text class="purchase-material">{{ item.materialName || item.materialCode }}</text>
										<text class="purchase-time" v-if="item.purchaseTime">{{ formatDate(item.purchaseTime) }}</text>
									</view>
									<view class="purchase-status" :class="getPurchaseStatusClassByStatus(item.purchaseStatus)">
										{{ getPurchaseStatusTextByStatus(item.purchaseStatus) }}
									</view>
								</view>
							</view>
							<view v-else class="empty-state">
								<text class="empty-text">暂无采购信息</text>
							</view>
						</view>
					</view>
				</swiper-item>

				<!-- 第四页：计划 -->
				<swiper-item class="swiper-item">
					<view class="carousel-page">
						<view class="page-title">
							<uv-icon name="calendar-fill" size="16" color="#909399"></uv-icon>
							<text class="title-text">生产计划</text>
						</view>

						<view class="process-section">
							<view class="process-header">
								<view class="process-status" :class="getPlanStatusClass()">
									{{ getPlanStatusText() }}
								</view>
								<view class="process-progress">
									<text class="progress-text">{{ getPlanProgress() }}%</text>
									<view class="progress-bar">
										<view class="progress-fill"
											:class="getPlanProgressClass()"
											:style="{ width: getPlanProgress() + '%' }">
										</view>
									</view>
								</view>
							</view>

							<view class="plan-info">
								<view class="plan-row" v-if="getPlanWorkshop()">
									<text class="plan-label">车间:</text>
									<text class="plan-value">{{ getPlanWorkshop() }}</text>
								</view>
								<view class="plan-row" v-if="getPlanProductionLine()">
									<text class="plan-label">产线:</text>
									<text class="plan-value">{{ getPlanProductionLine() }}</text>
								</view>
								<view class="plan-row" v-if="getPlanForeman()">
									<text class="plan-label">负责人:</text>
									<text class="plan-value">{{ getPlanForeman() }}</text>
								</view>
								<view class="plan-row" v-if="getPlanQuantity()">
									<text class="plan-label">计划数量:</text>
									<text class="plan-value">{{ getPlanQuantity() }}</text>
								</view>
								<view class="plan-row" v-if="getPlanFulfilledQty()">
									<text class="plan-label">已完成:</text>
									<text class="plan-value completed">{{ getPlanFulfilledQty() }}</text>
								</view>
							</view>

							<view class="schedule-list" v-if="getScheduleList().length > 0">
								<view class="schedule-title">计划安排</view>
								<view class="schedule-item" v-for="(schedule, index) in getScheduleList()" :key="index">
									<view class="schedule-header">
										<text class="schedule-stage">第{{ index + 1 }}阶段</text>
										<view class="schedule-status" :class="getScheduleStatusClassByStatus(schedule.status)">
											{{ getScheduleStatusTextByStatus(schedule.status) }}
										</view>
									</view>
									<view class="schedule-detail">
										<text class="schedule-time">{{ formatDate(schedule.startTime) }} ~ {{ formatDate(schedule.endTime) }}</text>
										<text class="schedule-quantity">计划: {{ schedule.quantity || 0 }}{{ getUnitName() }} | 已完成: {{ schedule.fulfilledQty || 0 }}{{ getUnitName() }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>

			<!-- 轮播指示器文字 -->
			<view class="carousel-indicator">
				<text class="indicator-text">{{ getCarouselTitle(currentSwiperIndex) }}</text>
			</view>
		</view>

		<view class="order-footer">
			<view class="footer-left">
				<view class="order-status" :class="[
					orderData.isException ? 'exception' :
					orderData.isOverdue ? 'overdue' :
					orderData.status || 'pending'
				]">
					{{
						orderData.isException ? '异常' :
						orderData.isOverdue ? '逾期' :
						orderData.status === 'completed' ? '已完成' :
						orderData.status === 'processing' ? '进行中' : '待处理'
					}}
				</view>
				<view class="order-actions">
					<uv-button
						size="mini"
						type="primary"
						plain
						@click.stop="handleOrderDetail"
					>
						详情
					</uv-button>
					<uv-button
						v-if="['processing', 'pending'].includes(orderData.status) && !orderData.isException"
						size="mini"
						type="warning"
						plain
						@click.stop="handleEdit"
					>
						编辑
					</uv-button>
				</view>
			</view>
			<view class="footer-right">
				<view class="overdue-info" v-if="orderData.isOverdue">
					<uv-icon name="warning" size="12" color="#f56c6c"></uv-icon>
					<text class="overdue-text">逾期</text>
				</view>
			</view>
		</view>

		<!-- 异常提示 -->
		<view v-if="orderData.isException" class="exception-alert">
			<uv-icon name="error-circle" size="14" color="#f56c6c"></uv-icon>
			<text class="exception-text">{{ orderData.exceptionMessage }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'OrderProcessItem',
	props: {
		orderData: {
			type: Object,
			required: true,
			default: () => ({})
		}
	},
	data() {
		return {
			currentSwiperIndex: 0
		}
	},
	methods: {
		// 格式化日期
		formatDate(date) {
			if (!date) return '-'
			const d = new Date(date)
			return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
		},

		// 轮播切换事件
		onSwiperChange(e) {
			this.currentSwiperIndex = e.detail.current
		},

		// 获取轮播标题
		getCarouselTitle(index) {
			const titles = ['订单详情', '原料库存', '采购进度', '生产计划']
			return titles[index] || '订单信息'
		},

		// 订单信息相关方法
		getCustomerName() {
			return this.orderData.salesOrderInfo?.customer?.name ||
				   this.orderData.customerName || '未知客户'
		},

		getOrderDate() {
			return this.orderData.salesOrderInfo?.orderDate || this.orderData.orderDate
		},

		getDeliveryTime() {
			return this.orderData.salesOrderInfo?.deliveryTime || this.orderData.deliveryTime
		},

		getRequirement() {
			return this.orderData.salesOrderInfo?.requirement ||
				   this.orderData.salesOrderInfo?.remark ||
				   this.orderData.requirement || '-'
		},

		// 产品信息相关方法
		getProductName() {
			return this.orderData.salesOrderInfo?.product?.name ||
				   this.orderData.productInfo || '暂无产品信息'
		},

		getProductCode() {
			return this.orderData.salesOrderInfo?.product?.fullCode ||
				   this.orderData.productCode || ''
		},

		getProductSpec() {
			return this.orderData.salesOrderInfo?.product?.spec ||
				   this.orderData.productSpec || ''
		},

		// 数量信息相关方法
		getOrderQuantity() {
			const quantity = this.orderData.salesOrderInfo?.product?.quantity ||
							this.orderData.quantity || 0
			const unit = this.orderData.salesOrderInfo?.product?.unitName ||
						this.orderData.unitName || ''
			return `${quantity}${unit}`
		},

		getCompletedQuantity() {
			const quantity = this.orderData.outbound?.quantity ||
							this.orderData.completedQuantity || 0
			const unit = this.orderData.salesOrderInfo?.product?.unitName ||
						this.orderData.unitName || ''
			return quantity > 0 ? `${quantity}${unit}` : ''
		},

		getSpecQuantity() {
			return this.orderData.salesOrderInfo?.product?.specQuantity || ''
		},

		getUnitName() {
			return this.orderData.salesOrderInfo?.product?.unitName ||
				   this.orderData.unitName || ''
		},

		// 需求状态相关方法
		getRequestStatus() {
			return this.orderData.request?.status
		},

		getRequestStatusText() {
			const status = this.orderData.request?.status
			const statusMap = {
				'0': '待开始',
				'1': '进行中',
				'2': '已暂停',
				'3': '已完成',
				'4': '已取消'
			}
			return statusMap[status] || '未知'
		},

		getRequestStatusClass() {
			const status = this.orderData.request?.status
			const classMap = {
				'0': 'status-pending',
				'1': 'status-active',
				'2': 'status-paused',
				'3': 'status-completed',
				'4': 'status-cancelled'
			}
			return classMap[status] || 'status-pending'
		},

		getRequestCreateTime() {
			return this.orderData.request?.createTime
		},

		getRequestConfirmTime() {
			return this.orderData.request?.confirmTime
		},

		// 订单详情
		handleOrderDetail() {
			this.$emit('detail', this.orderData)
		},

		// 编辑订单
		handleEdit() {
			this.$emit('edit', this.orderData)
		},

		// 原料库存相关方法
		getMaterialStatusText() {
			const status = this.orderData.materialInventory?.status
			const statusMap = {
				'0': '待分析',
				'1': '已分析',
				'2': '部分缺料',
				'3': '全部缺料',
				'4': '已备齐'
			}
			return statusMap[String(status)] || '待分析'
		},

		getMaterialStatusClass() {
			const status = this.orderData.materialInventory?.status
			const classMap = {
				'0': 'status-pending',
				'1': 'status-active',
				'2': 'status-warning',
				'3': 'status-danger',
				'4': 'status-completed'
			}
			return classMap[String(status)] || 'status-pending'
		},

		getMaterialProgress() {
			return Math.round(this.orderData.materialProgress || 0)
		},

		getMaterialProgressClass() {
			const progress = this.getMaterialProgress()
			if (progress >= 80) return 'progress-green'
			if (progress >= 60) return 'progress-yellow'
			return 'progress-red'
		},

		getMaterialList() {
			return this.orderData.materialInventory?.rawMaterials || []
		},

		// 采购相关方法
		getProcurementStatusText() {
			const status = this.orderData.procurement?.status
			const statusMap = {
				'0': '待开始',
				'1': '进行中',
				'2': '已暂停',
				'3': '已完成',
				'4': '已取消'
			}
			return statusMap[status] || '待开始'
		},

		getProcurementStatusClass() {
			const status = this.orderData.procurement?.status
			const classMap = {
				'0': 'status-pending',
				'1': 'status-active',
				'2': 'status-paused',
				'3': 'status-completed',
				'4': 'status-cancelled'
			}
			return classMap[status] || 'status-pending'
		},

		getProcurementProgress() {
			return Math.round(this.orderData.procurementProgress || 0)
		},

		getProcurementProgressClass() {
			const progress = this.getProcurementProgress()
			if (progress >= 80) return 'progress-green'
			if (progress >= 60) return 'progress-yellow'
			return 'progress-red'
		},

		getPurchaseList() {
			return this.orderData.procurement?.purchaseItems || []
		},

		getPurchaseStatusText(status) {
			const statusMap = {
				'0': '待审核',
				'1': '已审核',
				'2': '采购中',
				'3': '已完成',
				'4': '已取消'
			}
			return statusMap[status] || '待审核'
		},

		getPurchaseStatusClass(status) {
			const classMap = {
				'0': 'status-pending',
				'1': 'status-active',
				'2': 'status-active',
				'3': 'status-completed',
				'4': 'status-cancelled'
			}
			return classMap[status] || 'status-pending'
		},

		// 为模板中的循环提供的方法
		getPurchaseStatusTextByStatus(status) {
			return this.getPurchaseStatusText(status)
		},

		getPurchaseStatusClassByStatus(status) {
			return this.getPurchaseStatusClass(status)
		},

		// 生产计划相关方法
		getPlanStatusText() {
			const status = this.orderData.productionPlan?.status
			const statusMap = {
				'0': '待开始',
				'1': '进行中',
				'2': '已暂停',
				'3': '已完成',
				'4': '已取消'
			}
			return statusMap[status] || '待开始'
		},

		getPlanStatusClass() {
			const status = this.orderData.productionPlan?.status
			const classMap = {
				'0': 'status-pending',
				'1': 'status-active',
				'2': 'status-paused',
				'3': 'status-completed',
				'4': 'status-cancelled'
			}
			return classMap[status] || 'status-pending'
		},

		getPlanProgress() {
			return Math.round(this.orderData.productionPlanProgress || 0)
		},

		getPlanProgressClass() {
			const progress = this.getPlanProgress()
			if (progress >= 80) return 'progress-green'
			if (progress >= 60) return 'progress-yellow'
			return 'progress-red'
		},

		getPlanWorkshop() {
			return this.orderData.productionPlan?.workshop || ''
		},

		getPlanProductionLine() {
			return this.orderData.productionPlan?.productionLine || ''
		},

		getPlanForeman() {
			return this.orderData.productionPlan?.foreman || ''
		},

		getPlanQuantity() {
			const quantity = this.orderData.productionPlan?.plannedQty ||
							this.orderData.productionPlan?.quantity || 0
			const unit = this.getUnitName()
			return quantity > 0 ? `${quantity}${unit}` : ''
		},

		getPlanFulfilledQty() {
			const quantity = this.orderData.productionPlan?.fulfilledQty || 0
			const unit = this.getUnitName()
			return quantity > 0 ? `${quantity}${unit}` : ''
		},

		getScheduleList() {
			return this.orderData.productionPlan?.schedule || []
		},

		getScheduleStatusText(status) {
			const statusMap = {
				'0': '待开始',
				'1': '进行中',
				'2': '已暂停',
				'3': '已完成',
				'4': '已取消'
			}
			return statusMap[status] || '待开始'
		},

		getScheduleStatusClass(status) {
			const classMap = {
				'0': 'status-pending',
				'1': 'status-active',
				'2': 'status-paused',
				'3': 'status-completed',
				'4': 'status-cancelled'
			}
			return classMap[status] || 'status-pending'
		},

		// 为模板中的循环提供的方法
		getScheduleStatusTextByStatus(status) {
			return this.getScheduleStatusText(status)
		},

		getScheduleStatusClassByStatus(status) {
			return this.getScheduleStatusClass(status)
		}
	}
}
</script>

<style scoped>
.order-card {
	background: white;
	border-radius: 12px;
	padding: 16px;
	margin-bottom: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: transform 0.2s;
}

.order-card:active {
	transform: scale(0.98);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 12px;
}

.order-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.order-no {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.order-type-badge {
	display: inline-block;
	padding: 2px 8px;
	border-radius: 4px;
	font-size: 12px;
	color: white;
}

.order-type-badge.fulfillment {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.order-priority {
	display: inline-block;
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	color: white;
}

.priority-high {
	background-color: #f56c6c;
}

.priority-medium {
	background-color: #e6a23c;
}

.priority-low {
	background-color: #909399;
}

.order-quantity {
	text-align: right;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.quantity-main {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.quantity-completed {
	font-size: 12px;
	color: #67c23a;
}

.order-content {
	margin-bottom: 12px;
}

.customer-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8px;
}

.customer-name {
	margin-left: 6px;
	font-size: 14px;
	color: #666;
}

.order-date-text {
	font-size: 12px;
	color: #999;
}

.product-info {
	margin-bottom: 8px;
}

.product-main {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 4px;
}

.product-name {
	font-size: 14px;
	color: #333;
	font-weight: 500;
}

.product-code {
	font-size: 12px;
	color: #666;
	background: #f5f5f5;
	padding: 2px 6px;
	border-radius: 4px;
}

.product-spec {
	font-size: 12px;
	color: #999;
	margin-top: 2px;
}

.time-info {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.delivery-info, .requirement-info {
	display: flex;
	align-items: center;
	gap: 4px;
}

.delivery-text, .requirement-text {
	font-size: 12px;
	color: #666;
}

.requirement-text {
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 进度条 */
.order-progress {
	margin-bottom: 16px;
}

.progress-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 6px;
}

.progress-label {
	font-size: 14px;
	color: #666;
}

.progress-value {
	font-size: 14px;
	font-weight: 500;
	color: #409eff;
}

/* 轮播容器样式 */
.carousel-container {
	margin-bottom: 16px;
}

.carousel-swiper {
	height: 280px;
	border-radius: 8px;
	overflow: hidden;
	background: #fafafa;
}

.swiper-item {
	height: 100%;
}

.carousel-page {
	padding: 16px;
	height: 100%;
	overflow-y: auto;
}

.page-title {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
	padding-bottom: 8px;
	border-bottom: 1px solid #e4e7ed;
}

.title-text {
	margin-left: 8px;
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.carousel-indicator {
	text-align: center;
	margin-top: 8px;
}

.indicator-text {
	font-size: 12px;
	color: #909399;
}

/* 信息区块样式 */
.info-section {
	margin-bottom: 16px;
}

.section-title {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	margin-bottom: 8px;
	padding-left: 8px;
	border-left: 3px solid #409eff;
}

.info-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 6px 0;
	border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
	border-bottom: none;
}

.info-label {
	font-size: 13px;
	color: #666;
	min-width: 70px;
}

.info-value {
	font-size: 13px;
	color: #333;
	flex: 1;
	text-align: right;
}

/* 产品详情样式 */
.product-detail {
	background: white;
	padding: 12px;
	border-radius: 6px;
	border: 1px solid #e4e7ed;
}

.product-name {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	margin-bottom: 4px;
}

.product-code {
	font-size: 12px;
	color: #666;
	background: #f5f5f5;
	padding: 2px 6px;
	border-radius: 4px;
	display: inline-block;
	margin-bottom: 4px;
}

.product-spec {
	font-size: 12px;
	color: #999;
}

/* 数量详情样式 */
.quantity-detail {
	background: white;
	padding: 12px;
	border-radius: 6px;
	border: 1px solid #e4e7ed;
}

.quantity-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.quantity-row:last-child {
	margin-bottom: 0;
}

.quantity-label {
	font-size: 13px;
	color: #666;
}

.quantity-value {
	font-size: 13px;
	color: #333;
	font-weight: 500;
}

.quantity-value.completed {
	color: #67c23a;
}

/* 状态详情样式 */
.status-detail {
	background: white;
	padding: 12px;
	border-radius: 6px;
	border: 1px solid #e4e7ed;
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.status-item:last-child {
	margin-bottom: 0;
}

.status-label {
	font-size: 13px;
	color: #666;
	min-width: 70px;
}

.status-value {
	font-size: 13px;
	color: #333;
}

.status-badge {
	padding: 2px 8px;
	border-radius: 4px;
	font-size: 12px;
	color: white;
}

.status-pending {
	background-color: #909399;
}

.status-active {
	background-color: #409eff;
}

.status-paused {
	background-color: #e6a23c;
}

.status-completed {
	background-color: #67c23a;
}

.status-cancelled {
	background-color: #f56c6c;
}

.status-warning {
	background-color: #e6a23c;
}

.status-danger {
	background-color: #f56c6c;
}

/* 进度区块样式 */
.process-section {
	background: white;
	padding: 12px;
	border-radius: 6px;
	border: 1px solid #e4e7ed;
}

.process-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.process-status {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	color: white;
}

.process-progress {
	display: flex;
	align-items: center;
	gap: 8px;
}

.progress-bar {
	width: 60px;
	height: 6px;
	background-color: #e4e7ed;
	border-radius: 3px;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	border-radius: 3px;
	transition: width 0.3s ease;
}

.progress-green {
	background-color: #67c23a;
}

.progress-yellow {
	background-color: #e6a23c;
}

.progress-red {
	background-color: #f56c6c;
}

.progress-text {
	font-size: 12px;
	color: #666;
	min-width: 35px;
}

.empty-state {
	text-align: center;
	padding: 20px;
}

.empty-text {
	font-size: 13px;
	color: #999;
}

/* 原料列表样式 */
.material-list {
	margin-top: 12px;
}

.material-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 0;
	border-bottom: 1px solid #f0f0f0;
}

.material-item:last-child {
	border-bottom: none;
}

.material-info {
	flex: 1;
}

.material-name {
	font-size: 13px;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 2px;
}

.material-code {
	font-size: 11px;
	color: #999;
}

.material-quantity {
	text-align: right;
}

.quantity-text {
	font-size: 12px;
	color: #666;
	display: block;
	margin-bottom: 4px;
}

.material-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	color: white;
}

.material-status.shortage {
	background-color: #f56c6c;
}

.material-status.sufficient {
	background-color: #67c23a;
}

/* 采购列表样式 */
.purchase-list {
	margin-top: 12px;
}

.purchase-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 0;
	border-bottom: 1px solid #f0f0f0;
}

.purchase-item:last-child {
	border-bottom: none;
}

.purchase-info {
	flex: 1;
}

.purchase-material {
	font-size: 13px;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 2px;
}

.purchase-time {
	font-size: 11px;
	color: #999;
}

.purchase-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	color: white;
}

/* 计划信息样式 */
.plan-info {
	margin-top: 12px;
	background: #f8f9fa;
	padding: 12px;
	border-radius: 6px;
}

.plan-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 6px;
}

.plan-row:last-child {
	margin-bottom: 0;
}

.plan-label {
	font-size: 12px;
	color: #666;
	min-width: 60px;
}

.plan-value {
	font-size: 12px;
	color: #333;
}

.plan-value.completed {
	color: #67c23a;
	font-weight: 500;
}

/* 计划安排样式 */
.schedule-list {
	margin-top: 12px;
}

.schedule-title {
	font-size: 13px;
	font-weight: 500;
	color: #333;
	margin-bottom: 8px;
	padding-left: 8px;
	border-left: 2px solid #409eff;
}

.schedule-item {
	background: #f8f9fa;
	padding: 10px;
	border-radius: 6px;
	margin-bottom: 8px;
}

.schedule-item:last-child {
	margin-bottom: 0;
}

.schedule-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 6px;
}

.schedule-stage {
	font-size: 12px;
	font-weight: 500;
	color: #333;
}

.schedule-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	color: white;
}

.schedule-detail {
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.schedule-time, .schedule-quantity {
	font-size: 11px;
	color: #666;
}



/* 订单底部样式 */
.order-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 12px;
}

.footer-left {
	display: flex;
	align-items: center;
	gap: 12px;
}

.footer-right {
	display: flex;
	align-items: center;
}

.order-actions {
	display: flex;
	gap: 8px;
}

.order-status {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	font-weight: 500;
}

.order-status.pending {
	background-color: #f0f0f0;
	color: #666;
}

.order-status.processing {
	background-color: #e3f2fd;
	color: #409eff;
}

.order-status.completed {
	background-color: #e8f5e8;
	color: #67c23a;
}

.order-status.exception {
	background-color: #ffebee;
	color: #f56c6c;
}

.order-status.overdue {
	background-color: #fff5f5;
	color: #f56c6c;
	border: 1px solid #fecaca;
}

.overdue-info {
	display: flex;
	align-items: center;
	gap: 4px;
}

.overdue-text {
	font-size: 12px;
	color: #f56c6c;
}

/* 异常提示 */
.exception-alert {
	margin-top: 12px;
	padding: 8px 12px;
	background-color: #fff5f5;
	border: 1px solid #fecaca;
	border-radius: 6px;
	display: flex;
	align-items: center;
}

.exception-text {
	margin-left: 6px;
	font-size: 12px;
	color: #f56c6c;
}
</style>
