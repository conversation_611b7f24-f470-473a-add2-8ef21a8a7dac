<template>
	<view class="order-process-page">
		<!-- Tab导航 -->
		<view class="tab-section">
			<uv-tabs
				:list="tabList"
				:current="activeTab"
				@change="handleTabChange"
				:scrollable="false"
				line-color="#409eff"
				active-color="#409eff"
				inactive-color="#909399"
				:line-width="30"
				:line-height="3"
				item-style="padding-left: 15px; padding-right: 15px; height: 44px;"
			></uv-tabs>
		</view>

		<!-- 数据概览Tab -->
		<view v-show="activeTab === 0" class="overview-tab tab-content">
			<!-- 统计卡片 -->
			<view class="statistics-section">
			<view class="statistics-grid">
				<view class="stat-card">
					<view class="stat-icon total">
						<uv-icon name="file-text" size="24" color="#409eff"></uv-icon>
					</view>
					<view class="stat-content">
						<view class="stat-value">{{ statistics.totalOrders }}</view>
						<view class="stat-label">总履约数</view>
						<view class="stat-growth" :class="{ positive: statistics.totalGrowth > 0 }">
							<uv-icon :name="statistics.totalGrowth > 0 ? 'arrow-up' : 'arrow-down'" size="12"></uv-icon>
							{{ Math.abs(statistics.totalGrowth) }}%
						</view>
					</view>
				</view>

				<view class="stat-card">
					<view class="stat-icon completed">
						<uv-icon name="checkmark-circle" size="24" color="#67c23a"></uv-icon>
					</view>
					<view class="stat-content">
						<view class="stat-value">{{ statistics.completedOrders }}</view>
						<view class="stat-label">已完成履约</view>
						<view class="stat-growth" :class="{ positive: statistics.completedGrowth > 0 }">
							<uv-icon :name="statistics.completedGrowth > 0 ? 'arrow-up' : 'arrow-down'" size="12"></uv-icon>
							{{ Math.abs(statistics.completedGrowth) }}%
						</view>
					</view>
				</view>

				<view class="stat-card">
					<view class="stat-icon processing">
						<uv-icon name="play-circle" size="24" color="#e6a23c"></uv-icon>
					</view>
					<view class="stat-content">
						<view class="stat-value">{{ statistics.processingOrders }}</view>
						<view class="stat-label">履约中</view>
						<view class="stat-growth" :class="{ positive: statistics.processingGrowth > 0 }">
							<uv-icon :name="statistics.processingGrowth > 0 ? 'arrow-up' : 'arrow-down'" size="12"></uv-icon>
							{{ Math.abs(statistics.processingGrowth) }}%
						</view>
					</view>
				</view>

				<view class="stat-card">
					<view class="stat-icon overdue">
						<uv-icon name="warning" size="24" color="#f56c6c"></uv-icon>
					</view>
					<view class="stat-content">
						<view class="stat-value">{{ statistics.overdueOrders }}</view>
						<view class="stat-label">履约异常</view>
						<view class="stat-growth" :class="{ positive: statistics.overdueGrowth > 0 }">
							<uv-icon :name="statistics.overdueGrowth > 0 ? 'arrow-up' : 'arrow-down'" size="12"></uv-icon>
							{{ Math.abs(statistics.overdueGrowth) }}%
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 图表区域 -->
		<view class="chart-section">
			<view class="chart-container">
				<view class="trend-chart">
					<OrderTrendChart :statistics="statistics" />
				</view>
				<view class="pie-chart">
					<OrderStatusPieChart :statistics="statistics" />
				</view>
			</view>
		</view>
		</view>

		<!-- 订单列表Tab -->
		<view v-show="activeTab === 1" class="list-tab tab-content">
			<!-- 搜索栏 -->
			<view class="search-section">
				<uv-search
					shape="round"
					v-model="searchQuery.orderNo"
					placeholder="请输入订单号/客户名称/产品名称"
					clearable
					bgColor="#f5f5f5"
					@search="handleSearch"
					@custom="handleSearch"
					@clear="handleClear"
				></uv-search>
			</view>

			<!-- 状态筛选标签 -->
			<view class="filter-tabs">
				<uv-tabs
					:list="approvalStatusTabList"
					@click="handleTabStatusChange"
					keyName="name"
					:current="currentTabIndex"
				></uv-tabs>
			</view>

			<!-- 订单列表 -->
			<view class="order-list-section">
			<uv-list @scrolltolower="loadMore" :show-scrollbar="false">
				<uv-list-item v-for="(order, index) in orderList" :key="order.id || index">
					<OrderProcessItem
						:orderData="order"
						@detail="handleOrderDetail"
						@edit="handleEdit"
					/>
				</uv-list-item>
			</uv-list>

			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<uv-loading-icon mode="circle"></uv-loading-icon>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 空状态 -->
			<view v-if="!loading && orderList.length === 0" class="empty-container">
				<uv-empty mode="data" text="暂无订单数据"></uv-empty>
			</view>
		</view>


	</view>
</view>
</template>

<script>
import {
	getFulfillmentPageApi
} from '../../../../../api/scm/sale/orderprocess/index.js'
import OrderTrendChart from './components/OrderTrendChart.vue'
import OrderStatusPieChart from './components/OrderStatusPieChart.vue'
import OrderProcessItem from './components/OrderProcessItem.vue'
import { DICT_TYPE, getDictOptions } from '../../../../../utils/dict.js'

export default {
	components: {
		OrderTrendChart,
		OrderStatusPieChart,
		OrderProcessItem
	},
	data() {
		return {
			// Tab相关
			activeTab: 0,
			tabList: [
				{ name: '数据概览' },
				{ name: '订单列表' }
			],

			// 搜索和筛选
			searchQuery: {
				orderNo: '',
				customerName: '',
				productName: '',
				productCode: ''
			},

			filterParams: {
				fulfillmentStatus: '',
				fulfillmentStage: '',
				priority: '',
				isOverdue: '',
				approvalStatus: '',
				startDate: null,
				endDate: null
			},

			// 快速筛选
			activeQuickFilter: '',
			quickFilters: [],

		// 字典数据
		approvalStatusDict: [],

		// Tab相关
		currentTabIndex: 0,

			// 统计数据
			statistics: {
				totalOrders: 0,
				totalGrowth: 0,
				completedOrders: 0,
				completedGrowth: 0,
				processingOrders: 0,
				processingGrowth: 0,
				overdueOrders: 0,
				overdueGrowth: 0
			},

			// 订单数据
			orderList: [],
			loading: false,

			// 分页参数
			pageParams: {
				pageNo: 1,
				pageSize: 10
			},
			hasMore: true,

			// 订单总数
			orderTotals: {
				saleTotal: 0,
				purchaseTotal: 0,
				workTotal: 0,
				total: 0
			}
		}
	},

	computed: {
		// 审批状态Tab列表
		approvalStatusTabList() {
			const baseTabs = [
				{ name: '全部', value: '' }
			]

			// 如果字典数据还没加载，返回基础tab
			if (!this.approvalStatusDict || this.approvalStatusDict.length === 0) {
				return baseTabs
			}

			// 统计各种审批状态的数量
			const statusCounts = {}

			// 初始化计数器
			this.approvalStatusDict.forEach(dict => {
				statusCounts[dict.value] = 0
			})

			// 遍历订单列表统计各状态数量
			this.orderList.forEach(order => {
				const status = order.approvalStatus?.toString()
				if (status && statusCounts.hasOwnProperty(status)) {
					statusCounts[status]++
				}
			})

			// 根据字典数据生成tab选项
			const statusTabs = this.approvalStatusDict.map(dict => ({
				name: dict.label,
				value: dict.value,
				count: statusCounts[dict.value] || 0
			}))

			return [...baseTabs, ...statusTabs]
		}
	},

	onLoad() {
		console.log('订单履约页面加载，准备调用 getFulfillmentPageApi')
		// 延迟初始化，确保DOM完全渲染
		this.$nextTick(() => {
			setTimeout(() => {
				this.initData()
			}, 100)
		})
	},

	onPullDownRefresh() {
		this.handleRefresh()
		setTimeout(() => {
			uni.stopPullDownRefresh()
		}, 1000)
	},

	onReachBottom() {
		this.loadMore()
	},

	mounted() {
		// 确保uv-tabs组件完全挂载
		this.$nextTick(() => {
			console.log('页面组件已挂载')
		})
	},

	methods: {
		// Tab切换
		handleTabChange(event) {
			console.log('Tab切换事件:', event)
			const index = typeof event === 'object' ? (event.index !== undefined ? event.index : event.current) : event
			console.log('切换到tab索引:', index)

			if (index === this.activeTab) return

			try {
				this.activeTab = index
				console.log('当前活动tab:', this.activeTab)

				// 延迟执行，确保DOM更新完成
				this.$nextTick(() => {
					// 如果切换到列表tab且还没有加载数据，则加载数据
					if (index === 1 && this.orderList.length === 0) {
						console.log('切换到列表tab，开始加载数据')
						this.loadOrderData(true)
					}
				})
			} catch (error) {
				console.error('Tab切换失败:', error)
			}
		},

		// 初始化数据
		async initData() {
			// 初始化统计数据
			this.statistics = {
				totalOrders: 0,
				completedOrders: 0,
				processingOrders: 0,
				overdueOrders: 0,
				totalGrowth: 0,
				completedGrowth: 0,
				processingGrowth: 0,
				overdueGrowth: 0
			}

			// 加载字典数据
			await this.loadApprovalStatusDict()

			await this.loadOrderData()
		},

		// 加载审批状态字典数据
		async loadApprovalStatusDict() {
			try {
				const response = await getDictOptions(DICT_TYPE.APPROVE_STATUS)
				if (response && response.length > 0) {
					this.approvalStatusDict = response
				} else {
					// 如果API返回空数据，使用空数组
					this.approvalStatusDict = []
				}
			} catch (error) {
				console.error('加载审批状态字典失败:', error)
				// 出错时使用空数组
				this.approvalStatusDict = []
			}
		},

		// 加载订单数据
		async loadOrderData(reset = true) {
			if (this.loading) return

			try {
				this.loading = true

				if (reset) {
					this.pageParams.pageNo = 1
					this.orderList = []
					this.hasMore = true
				}

				// 构建查询参数
				const params = {
					...this.pageParams,
					orderNo: this.searchQuery.orderNo,
					customerName: this.searchQuery.customerName,
					productName: this.searchQuery.productName,
					productCode: this.searchQuery.productCode,
					...this.filterParams,
					quickFilter: this.activeQuickFilter,
					detail: true
				}

				// 加载订单履约数据
				const response = await getFulfillmentPageApi(params)

				if (response && (response.code === 200 || response.data)) {
					// 兼容不同的响应格式
					const responseData = response.data || response
					const newOrders = this.processOrderData(responseData.list || responseData.records || [])

					if (reset) {
						this.orderList = newOrders
					} else {
						this.orderList.push(...newOrders)
					}

					// 更新统计数据
					if (reset) {
						this.updateStatistics(responseData)
					}

					// 检查是否还有更多数据
					this.hasMore = newOrders.length >= this.pageParams.pageSize

					if (!reset) {
						this.pageParams.pageNo++
					}
				}
			} catch (error) {
				console.error('加载订单履约数据失败:', error)
				console.error('API调用: getFulfillmentPageApi')
				console.error('请求参数:', params)

				// 如果是第一次加载且失败，显示模拟数据
				if (reset && this.orderList.length === 0) {
					console.log('首次加载失败，使用模拟数据')
					this.loadMockData()
				} else {
					uni.showToast({
						title: '加载履约数据失败，请重试',
						icon: 'none'
					})
				}
			} finally {
				this.loading = false
			}
		},



		// 加载模拟订单履约数据（当API调用失败时使用）
		loadMockData() {
			const mockOrders = [
				{
					id: 1,
					orderNo: 'SO202401001',
					orderType: 'sale',
					customerName: '北京科技有限公司',
					totalAmount: 15800.00,
					orderDate: '2024-01-15',
					approvalStatus: '1',
					paymentStatus: '1',
					deliveryStatus: '0',
					productInfo: '有机肥料 100袋',
					progress: 70,
					isException: false,
					exceptionMessage: ''
				},
				{
					id: 2,
					orderNo: 'PO202401002',
					orderType: 'purchase',
					customerName: '上海农资供应商',
					totalAmount: 8500.00,
					orderDate: '2024-01-14',
					approvalStatus: '1',
					paymentStatus: '0',
					deliveryStatus: '0',
					productInfo: '种子 50包',
					progress: 35,
					isException: true,
					exceptionMessage: '订单超期未支付（已逾期3天）'
				},
				{
					id: 3,
					orderNo: 'WO202401003',
					orderType: 'work',
					customerName: '生产车间A',
					totalAmount: 0,
					orderDate: '2024-01-13',
					approvalStatus: '1',
					paymentStatus: '1',
					deliveryStatus: '1',
					productInfo: '有机肥生产任务',
					progress: 100,
					isException: false,
					exceptionMessage: ''
				}
			]

			this.orderList = mockOrders
			this.hasMore = false

			console.log('已加载模拟数据')
		},

		// 处理订单履约数据
		processOrderData(orders) {
			return orders.map(order => ({
				id: order.id,
				orderNo: order.salesOrderInfo?.orderNo || 'N/A',
				orderType: 'fulfillment',
				customerName: order.salesOrderInfo?.customer?.name || 'N/A',
				orderDate: order.salesOrderInfo?.orderDate || Date.now(),
				deliveryTime: order.salesOrderInfo?.deliveryTime || null,
				productInfo: order.salesOrderInfo?.product?.name || 'N/A',
				productCode: order.salesOrderInfo?.product?.fullCode || order.salesOrderInfo?.product?.code || '',
				productSpec: order.salesOrderInfo?.product?.spec || '',
				quantity: order.salesOrderInfo?.product?.quantity || 0,
				unitName: order.salesOrderInfo?.product?.unitName || '',
				requirement: order.salesOrderInfo?.requirement || '',
				remark: order.salesOrderInfo?.remark || '',
				priority: order.priority || 'medium',
				completedQuantity: order.outbound?.quantity || 0,

				// 各阶段状态和进度
				materialStatus: order.materialInventory?.status || 0,
				materialProgress: order.materialInventory?.progress || 0,
				procurementStatus: order.procurement?.status || 0,
				procurementProgress: order.procurement?.progress || 0,
				productionPlanStatus: order.productionPlan?.status || 0,
				productionPlanProgress: order.productionPlan?.progress || 0,
				productionStatus: order.productionExecution?.status || 0,
				productionProgress: order.productionExecution?.progress || 0,
				qualityStatus: order.qualityInspection?.status || 0,
				qualityProgress: order.qualityInspection?.progress || 0,
				warehouseStatus: order.warehousing?.status || 0,
				warehouseProgress: order.warehousing?.progress || 0,
				deliveryStatus: order.delivery?.status || 0,
				deliveryProgress: order.delivery?.progress || 0,

				// 原始数据保存，用于详细展示
				rawData: order,

				// 计算整体状态
				progress: this.calculateOverallProgress(order),
				status: this.calculateOverallStatus(order),
				isException: this.checkFulfillmentException(order),
				isOverdue: this.checkOverdue(order),
				exceptionMessage: this.getFulfillmentExceptionMessage(order)
			}))
		},

		// 更新统计数据
		updateStatistics(data) {
			// 使用接口返回的总数量
			this.statistics.totalOrders = data.total || data.totalCount || data.totalElements || 0

			// 计算各种状态的履约订单数量
			const orders = this.orderList || []

			// 根据履约状态计算
			this.statistics.completedOrders = orders.filter(order =>
				order.status === 'completed' || order.progress >= 100
			).length

			this.statistics.processingOrders = orders.filter(order =>
				order.status === 'processing' || (order.progress > 0 && order.progress < 100)
			).length

			this.statistics.overdueOrders = orders.filter(order =>
				order.isException || order.isOverdue
			).length

			// 模拟增长率数据
			this.statistics.totalGrowth = this.generateGrowthRate()
			this.statistics.completedGrowth = this.generateGrowthRate()
			this.statistics.processingGrowth = this.generateGrowthRate()
			this.statistics.overdueGrowth = this.generateGrowthRate(true)
		},

		// 生成增长率
		generateGrowthRate(isException = false) {
			if (isException) {
				return Number((Math.random() * 8 - 4).toFixed(1)) // -4% 到 +4%
			}
			return Number((Math.random() * 15 + 2).toFixed(1)) // 2% 到 17%
		},

		// 获取产品信息
		getProductInfo(order) {
			if (order.orderDetails && order.orderDetails.length > 0) {
				const firstDetail = order.orderDetails[0]
				return `${firstDetail.materialName || '未知产品'} ${firstDetail.quantity || 0}${firstDetail.unit || ''}`
			}
			const remark = order.remarks || order.remark
			if (remark) {
				return remark.length > 30 ? remark.substring(0, 30) + '...' : remark
			}
			return `${order.customerName || order.supplierName || '未知客户'} 的订单`
		},

		// 计算履约整体进度
		calculateOverallProgress(order) {
			const stages = [
				{ weight: 10, progress: order.materialInventory?.progress || 0 }, // 物料准备
				{ weight: 15, progress: order.procurement?.progress || 0 }, // 采购
				{ weight: 25, progress: order.productionPlan?.progress || 0 }, // 生产计划
				{ weight: 20, progress: order.productionExecution?.progress || 0 }, // 生产执行
				{ weight: 10, progress: order.qualityInspection?.progress || 0 }, // 质检
				{ weight: 10, progress: order.warehousing?.progress || 0 }, // 入库
				{ weight: 10, progress: order.delivery?.progress || 0 } // 交付
			]

			let totalWeightedProgress = 0
			let totalWeight = 0

			stages.forEach(stage => {
				// 将进度限制在100%以内
				const normalizedProgress = Math.min(stage.progress, 100)
				totalWeightedProgress += stage.weight * normalizedProgress
				totalWeight += stage.weight
			})

			return totalWeight > 0 ? Math.round(totalWeightedProgress / totalWeight) : 0
		},

		// 计算履约整体状态
		calculateOverallStatus(order) {
			// 如果交付完成，状态为已完成
			if (order.delivery?.status >= 3 || order.delivery?.progress >= 100) {
				return 'completed'
			}

			// 如果有任何阶段在进行中，状态为进行中
			const stages = [
				order.materialInventory?.status,
				order.procurement?.status,
				order.productionPlan?.status,
				order.productionExecution?.status,
				order.qualityInspection?.status,
				order.warehousing?.status,
				order.delivery?.status
			]

			if (stages.some(status => status > 0 && status < 3)) {
				return 'processing'
			}

			// 默认为待处理
			return 'pending'
		},

		// 检查异常
		checkException(order) {
			if (order.approvalStatus === '2') return true // 已拒绝
			if (order.paymentStatus === '0' && order.orderDate) { // 未支付
				const daysDiff = Math.floor((Date.now() - new Date(order.orderDate).getTime()) / (1000 * 60 * 60 * 24))
				if (daysDiff > 7) return true // 超过7天未支付
			}
			// 检查交货延期
			if (order.estimatedDeliveryDate || order.deliveryDate) {
				const deliveryDate = new Date(order.estimatedDeliveryDate || order.deliveryDate)
				if (deliveryDate < new Date() && order.deliveryStatus !== '1') {
					return true // 已过交货期但未发货
				}
			}
			return false
		},

		// 检查履约异常
		checkFulfillmentException(order) {
			// 检查物料短缺
			if (order.materialInventory?.rawMaterials?.some(material => material.shortage)) {
				return true
			}

			// 检查质检不合格率过高
			if (order.qualityInspection?.unqualifiedQty > 0 &&
				order.qualityInspection?.qualifiedQty > 0) {
				const unqualifiedRate = order.qualityInspection.unqualifiedQty /
					(order.qualityInspection.qualifiedQty + order.qualityInspection.unqualifiedQty)
				if (unqualifiedRate > 0.1) { // 不合格率超过10%
					return true
				}
			}

			// 检查交付延期
			if (order.salesOrderInfo?.deliveryTime &&
				order.delivery?.status < 3 &&
				Date.now() > order.salesOrderInfo.deliveryTime) {
				return true
			}

			return false
		},

		// 检查逾期
		checkOverdue(order) {
			// 对于履约数据，检查交付时间
			if (order.salesOrderInfo?.deliveryTime) {
				return Date.now() > order.salesOrderInfo.deliveryTime &&
					   (order.delivery?.status || 0) < 3
			}

			// 兼容原有逻辑
			if (order.estimatedDeliveryDate || order.deliveryDate) {
				const deliveryDate = new Date(order.estimatedDeliveryDate || order.deliveryDate)
				return deliveryDate < new Date() && order.deliveryStatus !== '1'
			}
			return false
		},

		// 获取履约异常信息
		getFulfillmentExceptionMessage(order) {
			const messages = []

			// 检查物料短缺
			const shortageItems = order.materialInventory?.rawMaterials?.filter(material => material.shortage) || []
			if (shortageItems.length > 0) {
				messages.push(`${shortageItems.length}种物料短缺`)
			}

			// 检查质检不合格率
			if (order.qualityInspection?.unqualifiedQty > 0 &&
				order.qualityInspection?.qualifiedQty > 0) {
				const unqualifiedRate = order.qualityInspection.unqualifiedQty /
					(order.qualityInspection.qualifiedQty + order.qualityInspection.unqualifiedQty)
				if (unqualifiedRate > 0.1) {
					messages.push(`质检不合格率${(unqualifiedRate * 100).toFixed(1)}%`)
				}
			}

			// 检查交付延期
			if (order.salesOrderInfo?.deliveryTime &&
				order.delivery?.status < 3 &&
				Date.now() > order.salesOrderInfo.deliveryTime) {
				const overdueDays = Math.floor((Date.now() - order.salesOrderInfo.deliveryTime) / (1000 * 60 * 60 * 24))
				messages.push(`交付延期${overdueDays}天`)
			}

			return messages.length > 0 ? messages.join('; ') : '履约异常'
		},

		// 获取异常信息
		getExceptionMessage(order) {
			if (order.approvalStatus === '2') return '订单审批被拒绝'
			if (order.paymentStatus === '0' && order.orderDate) {
				const daysDiff = Math.floor((Date.now() - new Date(order.orderDate).getTime()) / (1000 * 60 * 60 * 24))
				if (daysDiff > 7) return `订单超期未支付（已逾期${daysDiff}天）`
			}
			if (order.estimatedDeliveryDate || order.deliveryDate) {
				const deliveryDate = new Date(order.estimatedDeliveryDate || order.deliveryDate)
				if (deliveryDate < new Date() && order.deliveryStatus !== '1') {
					const delayDays = Math.floor((Date.now() - deliveryDate.getTime()) / (1000 * 60 * 60 * 24))
					return `交货延期（已延期${delayDays}天）`
				}
			}
			return '订单处理异常'
		},

		// 搜索处理
		handleSearch() {
			this.loadOrderData(true)
		},

		handleClear() {
			this.searchQuery = {
				orderNo: '',
				customerName: '',
				productName: '',
				productCode: ''
			}
			this.loadOrderData(true)
		},

		// 快速筛选处理
		handleQuickFilter(filterKey) {
			this.activeQuickFilter = filterKey
			this.loadOrderData(true)
		},

		// 审批状态筛选处理
		handleApprovalStatusFilter(filterKey) {
			this.activeQuickFilter = filterKey

			// 设置筛选参数
			this.filterParams.approvalStatus = filterKey

			// 重新加载数据
			this.loadOrderData(true)
		},

		// 处理Tab状态切换
		handleTabStatusChange(event) {
			const index = event.index || 0
			const tabItem = this.approvalStatusTabList[index]

			if (tabItem) {
				this.currentTabIndex = index
				this.activeQuickFilter = tabItem.value
				this.filterParams.approvalStatus = tabItem.value
				this.loadOrderData(true)
			}
		},

		// 刷新数据
		handleRefresh() {
			this.loadOrderData(true)
			uni.showToast({
				title: '刷新成功',
				icon: 'success',
				duration: 1500
			})
		},

		// 加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.loadOrderData(false)
			}
		},

		// 导出数据
		exportData() {
			uni.showToast({
				title: '导出功能开发中',
				icon: 'none'
			})
		},

		// 订单详情
		handleOrderDetail(order) {
			// 暂时显示订单信息，后续可以跳转到详情页
			uni.showModal({
				title: '订单详情',
				content: `订单号: ${order.orderNo}\n客户: ${order.customerName}\n金额: ¥${this.formatAmount(order.totalAmount)}`,
				showCancel: false
			})
		},



		// 工具方法 - 保留一些可能在JS中需要用到的方法
		formatAmount(amount) {
			if (!amount) return '0.00'
			return Number(amount).toFixed(2)
		},

		formatDate(date) {
			if (!date) return ''
			const d = new Date(date)
			return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
		},

		// 编辑订单
		handleEdit(order) {
			console.log('编辑订单:', order)
			uni.showToast({
				title: '编辑功能开发中',
				icon: 'none'
			})
		}
	}
}
</script>

<style scoped>
.order-process-page {
	background-color: #f8f9fa;
	min-height: 100vh;
}



/* 统计卡片 */
.statistics-section {
	padding: 16px 16px 16px;
}

.statistics-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 12px;
}

.stat-card {
	background: white;
	border-radius: 12px;
	padding: 16px;
	display: flex;
	align-items: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: transform 0.2s;
}

.stat-card:active {
	transform: scale(0.98);
}

.stat-icon {
	width: 48px;
	height: 48px;
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
}

.stat-icon.total {
	background-color: #e3f2fd;
}

.stat-icon.completed {
	background-color: #e8f5e8;
}

.stat-icon.processing {
	background-color: #fff3e0;
}

.stat-icon.overdue {
	background-color: #ffebee;
}

.stat-content {
	flex: 1;
}

.stat-value {
	font-size: 24px;
	font-weight: bold;
	color: #333;
	line-height: 1;
}

.stat-label {
	font-size: 14px;
	color: #666;
	margin: 4px 0;
}

.stat-growth {
	font-size: 12px;
	color: #f56c6c;
	display: flex;
	align-items: center;
}

.stat-growth.positive {
	color: #67c23a;
}

/* 图表区域 */
.chart-section {
	padding: 0 16px 16px;
}

.chart-container {
	display: flex;
	gap: 16px;
	height: 300px;
}

.trend-chart {
	flex: 2;
}

.pie-chart {
	flex: 1;
}

/* 响应式设计 - 小屏幕下图表垂直排列 */
@media (max-width: 768px) {
	.chart-container {
		flex-direction: column;
		height: auto;
		gap: 16px;
	}

	.trend-chart,
	.pie-chart {
		flex: none;
		height: 300px;
	}
}

/* 订单列表 */
.order-list-section {
	padding: 16px;
}



/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20px;
}

.loading-text {
	margin-top: 8px;
	font-size: 14px;
	color: #666;
}

/* 空状态 */
.empty-container {
	padding: 40px 20px;
}





.stage-item.pending {
	background: #f5f5f5;
	color: #999;
	border-color: #e0e0e0;
}

.stage-item.active {
	background: #e3f2fd;
	color: #1976d2;
	border-color: #1976d2;
}

.stage-item.completed {
	background: #e8f5e8;
	color: #4caf50;
	border-color: #4caf50;
}

.stage-name {
	font-size: 11px;
	font-weight: 500;
}

.stage-progress {
	font-size: 10px;
	opacity: 0.8;
}

/* 订单底部样式 */
.order-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 12px;
}

.footer-left {
	display: flex;
	align-items: center;
	gap: 12px;
}

.footer-right {
	display: flex;
	align-items: center;
}

.order-actions {
	display: flex;
	gap: 8px;
}

.overdue-info {
	display: flex;
	align-items: center;
	gap: 4px;
}

.overdue-text {
	font-size: 12px;
	color: #f56c6c;
}

.order-status.overdue {
	background-color: #fff5f5;
	color: #f56c6c;
	border: 1px solid #fecaca;
}

/* 订单数量样式 */
.order-quantity {
	font-size: 14px;
	color: #666;
	font-weight: 500;
}

/* 产品规格样式 */
.product-spec {
	font-size: 12px;
	color: #999;
	margin-top: 2px;
}

/* 交付信息样式 */
.delivery-info {
	display: flex;
	align-items: center;
	margin-top: 8px;
}

.delivery-text {
	margin-left: 4px;
	font-size: 12px;
	color: #666;
}

/* 履约类型徽章样式 */
.order-type-badge.fulfillment {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

/* Tab相关样式 */
.tab-section {
	background: white;
	border-bottom: 1px solid #ebeef5;
	position: sticky;
	top: 0;
	z-index: 100;
}

.tab-content {
	min-height: calc(100vh - 100px);
	opacity: 1;
	transition: opacity 0.3s ease;
}

.overview-tab, .list-tab {
	animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 搜索栏样式 */
.search-section {
	padding: 10px;
	background-color: #fff;
	border-bottom: 1px solid #eee;
}

/* 筛选标签样式 */
.filter-tabs {
	background-color: #fff;
	border-bottom: 1px solid #eee;
}



/* 订单卡片增强样式 */
.order-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 12px;
}

.order-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.order-priority {
	display: inline-block;
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	color: white;
}

.priority-high {
	background-color: #f56c6c;
}

.priority-medium {
	background-color: #e6a23c;
}

.priority-low {
	background-color: #909399;
}

.order-quantity {
	text-align: right;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.quantity-main {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.quantity-completed {
	font-size: 12px;
	color: #67c23a;
}

.customer-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8px;
}

.order-date-text {
	font-size: 12px;
	color: #999;
}

.product-main {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 4px;
}

.product-name {
	font-size: 14px;
	color: #333;
	font-weight: 500;
}

.product-code {
	font-size: 12px;
	color: #666;
	background: #f5f5f5;
	padding: 2px 6px;
	border-radius: 4px;
}

.time-info {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.delivery-info, .requirement-info {
	display: flex;
	align-items: center;
	gap: 4px;
}

.delivery-text, .requirement-text {
	font-size: 12px;
	color: #666;
}

.requirement-text {
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
</style>
